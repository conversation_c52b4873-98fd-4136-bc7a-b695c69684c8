﻿namespace NRedisStack.Json.Literals
{
    internal class JSON
    {
        public const string ARRAPPEND = "JSON.ARRAPPEND";
        public const string ARRINDEX = "JSON.ARRINDEX";
        public const string ARRINSERT = "JSON.ARRINSERT";
        public const string ARRLEN = "JSON.ARRLEN";
        public const string ARRPOP = "JSON.ARRPOP";
        public const string ARRTRIM = "JSON.ARRTRIM";
        public const string CLEAR = "JSON.CLEAR";
        public const string DEBUG = "JSON.DEBUG";
        public const string DEBUG_HELP = "JSON.DEBUG HELP";
        public const string DEL = "JSON.DEL";
        public const string FORGET = "JSON.FORGET";
        public const string GET = "JSON.GET";
        public const string MEMORY = "MEMORY";
        public const string MERGE = "JSON.MERGE";
        public const string MSET = "JSON.MSET";
        public const string MGET = "JSON.MGET";
        public const string NUMINCRBY = "JSON.NUMINCRBY";
        public const string NUMMULTBY = "JSON.NUMMULTBY";
        public const string OBJKEYS = "JSON.OBJKEYS";
        public const string OBJLEN = "JSON.OBJLEN";
        public const string RESP = "JSON.RESP";
        public const string SET = "JSON.SET";
        public const string STRAPPEND = "JSON.STRAPPEND";
        public const string STRLEN = "JSON.STRLEN";
        public const string TOGGLE = "JSON.TOGGLE";
        public const string TYPE = "JSON.TYPE";
    }
}