﻿namespace NRedisStack.Literals
{
    internal class TDIGEST
    {
        public const string CREATE = "TDIGEST.CREATE";
        public const string RESET = "TDIGEST.RESET";
        public const string ADD = "TDIGEST.ADD";
        public const string MERGE = "TDIGEST.MERGE";
        public const string MERGESTORE = "TDIGEST.MERGESTORE";
        public const string MIN = "TDIGEST.MIN";
        public const string MAX = "TDIGEST.MAX";
        public const string QUANTILE = "TDIGEST.QUANTILE";
        public const string CDF = "TDIGEST.CDF";
        public const string TRIMMED_MEAN = "TDIGEST.TRIMMED_MEAN";
        public const string INFO = "TDIGEST.INFO";
        public const string RANK = "TDIGEST.RANK";
        public const string REVRANK = "TDIGEST.REVRANK";
        public const string BYRANK = "TDIGEST.BYRANK";
        public const string BYREVRANK = "TDIGEST.BYREVRANK";
    }
}