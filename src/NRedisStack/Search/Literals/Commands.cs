﻿namespace NRedisStack.Search.Literals;

internal class FT
{
    public const string _LIST = "FT._LIST";
    public const string AGGREGATE = "FT.AGGREGATE";
    public const string ALIASADD = "FT.ALIASADD";
    public const string ALIASDEL = "FT.ALIASDEL";
    public const string ALIASUPDATE = "FT.ALIASUPDATE";
    public const string ALTER = "FT.ALTER";
    public const string CONFIG = "FT.CONFIG";
    public const string CREATE = "FT.CREATE";
    public const string CURSOR = "FT.CURSOR";
    public const string DICTADD = "FT.DICTADD";
    public const string DICTDEL = "FT.DICTDEL";
    public const string DICTDUMP = "FT.DICTDUMP";
    public const string DROPINDEX = "FT.DROPINDEX";
    public const string EXPLAIN = "FT.EXPLAIN";
    public const string EXPLAINCLI = "FT.EXPLAINCLI";
    public const string INFO = "FT.INFO";
    public const string PROFILE = "FT.PROFILE";
    public const string SEARCH = "FT.SEARCH";
    public const string SPELLCHECK = "FT.SPELLCHECK";
    public const string SUGADD = "FT.SUGADD";
    public const string SUGDEL = "FT.SUGDEL";
    public const string SUGGET = "FT.SUGGET";
    public const string SUGLEN = "FT.SUGLEN";
    public const string SYNDUMP = "FT.SYNDUMP";
    public const string SYNUPDATE = "FT.SYNUPDATE";
    public const string TAGVALS = "FT.TAGVALS";
}