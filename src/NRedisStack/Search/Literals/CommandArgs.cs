namespace NRedisStack.Search.Literals;

internal class SearchArgs
{
    public const string AGGREGATE = "AGGREGATE";
    public const string APPLY = "APPLY";
    public const string AS = "AS";
    public const string ASC = "ASC";
    public const string CASESENSITIVE = "CASESENSITIVE";
    public const string COUNT = "COUNT";
    public const string DESC = "DESC";
    public const string DIALECT = "DIALECT";
    public const string DISTANCE = "DISTANCE";
    public const string EXCLUDE = "EXCLUDE";
    public const string EXPANDER = "EXPANDER";
    public const string FIELDS = "FIELDS";
    public const string FILTER = "FILTER";
    public const string FRAGS = "FRAGS";
    public const string FUZZY = "FUZZY";
    public const string GROUPBY = "GROUPBY";
    public const string HIGHLIGHT = "HIGHLIGHT";
    public const string INCLUDE = "INCLUDE";
    public const string INCR = "INCR";
    public const string INFIELDS = "INFIELDS";
    public const string INKEYS = "INKEYS";
    public const string INORDER = "INORDER";
    public const string JSON = "JSON";
    public const string LANGUAGE = "LANGUAGE";
    public const string LANGUAGE_FIELD = "LANGUAGE_FIELD";
    public const string LEN = "LEN";
    public const string LIMIT = "LIMIT";
    public const string LIMITED = "LIMITED";
    public const string LOAD = "LOAD";
    public const string MAX = "MAX";
    public const string MAXIDLE = "MAXIDLE";
    public const string MAXTEXTFIELDS = "MAXTEXTFIELDS";
    public const string NOCONTENT = "NOCONTENT";
    public const string NOFIELDS = "NOFIELDS";
    public const string NOFREQS = "NOFREQS";
    public const string NOHL = "NOHL";
    public const string NOINDEX = "NOINDEX";
    public const string NOOFFSETS = "NOOFFSETS";
    public const string NOSTEM = "NOSTEM";
    public const string NOSTOPWORDS = "NOSTOPWORDS";
    public const string ON_HASH = "ON HASH";
    public const string PARAMS = "PARAMS";
    public const string PAYLOAD = "PAYLOAD";
    public const string PAYLOAD_FIELD = "PAYLOAD_FIELD";
    public const string PHONETIC = "PHONETIC";
    public const string PREFIX = "PREFIX";
    public const string QUERY = "QUERY";
    public const string RETURN = "RETURN";
    public const string SCORE = "SCORE";
    public const string SCORE_FIELD = "SCORE_FIELD";
    public const string SCORER = "SCORER";
    public const string SEARCH = "SEARCH";
    public const string SEPARATOR = "SEPARATOR";
    public const string SKIPINITIALSCAN = "SKIPINITIALSCAN";
    public const string SLOP = "SLOP";
    public const string SORTBY = "SORTBY";
    public const string STOPWORDS = "STOPWORDS";
    public const string SUMMARIZE = "SUMMARIZE";
    public const string TAGS = "TAGS";
    public const string TEMPORARY = "TEMPORARY";
    public const string TERMS = "TERMS";
    public const string TIMEOUT = "TIMEOUT";
    public const string UNF = "UNF";
    public const string VERBATIM = "VERBATIM";
    public const string WEIGHT = "WEIGHT";
    public const string WITHCURSOR = "WITHCURSOR";
    public const string WITHPAYLOADS = "WITHPAYLOADS";
    public const string WITHSCORES = "WITHSCORES";
    public const string WITHSUFFIXTRIE = "WITHSUFFIXTRIE";
}

