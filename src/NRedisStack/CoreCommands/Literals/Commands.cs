namespace NRedisStack.Core.Literals
{
    /// <summary>
    /// Redis Core command literals
    /// </summary>
    internal static class RedisCoreCommands
    {
        public const string BLMOVE = "BLMOVE";
        public const string BLMPOP = "BLMPOP";
        public const string BLPOP = "BLPOP";
        public const string BRPOP = "BRPOP";
        public const string BRPOPLPUSH = "BRPOPLPUSH";
        public const string BZMPOP = "BZMPOP";
        public const string BZPOPMAX = "BZPOPMAX";
        public const string BZPOPMIN = "BZPOPMIN";
        public const string CLIENT = "CLIENT";
        public const string SETINFO = "SETINFO";
        public const string XREAD = "XREAD";
        public const string XREADGROUP = "XREADGROUP";
    }
}
