﻿namespace NRedisStack.CuckooFilter.Literals
{
    internal class CF
    {
        public const string RESERVE = "CF.RESERVE";
        public const string ADD = "CF.ADD";
        public const string ADDNX = "CF.ADDNX";
        public const string INSERT = "CF.INSERT";
        public const string INSERTNX = "CF.INSERTNX";
        public const string EXISTS = "CF.EXISTS";
        public const string MEXISTS = "CF.MEXISTS";
        public const string DEL = "CF.DEL";
        public const string COUNT = "CF.COUNT";
        public const string SCANDUMP = "CF.SCANDUMP";
        public const string LOADCHUNK = "CF.LOADCHUNK";
        public const string INFO = "CF.INFO";
    }
}