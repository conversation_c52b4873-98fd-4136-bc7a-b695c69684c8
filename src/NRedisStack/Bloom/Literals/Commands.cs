﻿namespace NRedisStack.Bloom.Literals
{
    internal class BF
    {
        public const string ADD = "BF.ADD";
        public const string CARD = "BF.CARD";
        public const string EXISTS = "BF.EXISTS";
        public const string INFO = "BF.INFO";
        public const string INSERT = "BF.INSERT";
        public const string LOADCHUNK = "BF.LOADCHUNK";
        public const string MADD = "BF.MADD";
        public const string MEXISTS = "BF.MEXISTS";
        public const string RESERVE = "BF.RESERVE";
        public const string SCANDUMP = "BF.SCANDUMP";
    }
}