﻿namespace NRedisStack.Literals
{
    internal class TS
    {
        public const string CREATE = "TS.CREATE";
        public const string ALTER = "TS.ALTER";
        public const string ADD = "TS.ADD";
        public const string MADD = "TS.MADD";
        public const string INCRBY = "TS.INCRBY";
        public const string DECRBY = "TS.DECRBY";
        public const string DEL = "TS.DEL";
        public const string CREATERULE = "TS.CREATERULE";
        public const string DELETERULE = "TS.DELETERULE";
        public const string RANGE = "TS.RANGE";
        public const string REVRANGE = "TS.REVRANGE";
        public const string MRANGE = "TS.MRANGE";
        public const string MREVRANGE = "TS.MREVRANGE";
        public const string GET = "TS.GET";
        public const string MGET = "TS.MGET";
        public const string INFO = "TS.INFO";
        public const string QUERYINDEX = "TS.QUERYINDEX";
    }
}
