namespace NRedisStack.Literals
{
    internal class TimeSeriesArgs
    {
        public const string RETENTION = "RETENTION";
        public const string LABELS = "LABELS";
        public const string UNCOMPRESSED = "UNCOMPRESSED";
        public const string COMPRESSED = "COMPRESSED";
        public const string COUNT = "COUNT";
        public const string AGGREGATION = "AGGREGATION";
        public const string ALIGN = "ALIGN";
        public const string FILTER = "FILTER";
        public const string WITHLABELS = "WITHLABELS";
        public const string SELECTEDLABELS = "SELECTED_LABELS";
        public const string TIMESTAMP = "TIMESTAMP";
        public const string CHUNK_SIZE = "CHUNK_SIZE";
        public const string DUPLICATE_POLICY = "DUPLICATE_POLICY";
        public const string ON_DUPLICATE = "ON_DUPLICATE";
        public const string GROPUBY = "GROUPBY";
        public const string REDUCE = "REDUCE";
        public const string FILTER_BY_TS = "FILTER_BY_TS";
        public const string FILTER_BY_VALUE = "FILTER_BY_VALUE";
        public const string LATEST = "LATEST";
        public const string DEBUG = "DEBUG";
        public const string BUCKETTIMESTAMP = "BUCKETTIMESTAMP";
        public const string EMPTY = "EMPTY";
        public const String IGNORE = "IGNORE";
    }
}
