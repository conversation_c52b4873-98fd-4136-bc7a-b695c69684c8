﻿namespace NRedisStack.TopK.Literals
{
    internal class TOPK
    {
        public const string RESERVE = "TOPK.RESERVE";
        public const string ADD = "TOPK.ADD";
        public const string INCRBY = "TOPK.INCRBY";
        public const string QUERY = "TOPK.QUERY";
        public const string COUNT = "TOPK.COUNT";
        public const string LIST = "TOPK.LIST";
        public const string INFO = "TOPK.INFO";
    }
}