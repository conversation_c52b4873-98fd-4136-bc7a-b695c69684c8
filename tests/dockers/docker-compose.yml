---

services:

  redis:
    image: ${CLIENT_LIBS_TEST_IMAGE:-redislabs/client-libs-test:rs-7.4.0-v1}
    container_name: redis-standalone
    environment:
      - TLS_ENABLED=yes
      - REDIS_CLUSTER=no
      - PORT=6379
      - TLS_PORT=6666
    command: ${REDIS_EXTRA_ARGS:---enable-debug-command yes --enable-module-command yes --tls-auth-clients optional --save ""}
    ports:
      - 6379:6379
      - 6666:6666 # TLS port
    volumes:
      - "./standalone:/redis/work"
    profiles:
      - standalone
      - all

  cluster:
    image: ${CLIENT_LIBS_TEST_IMAGE:-redislabs/client-libs-test:rs-7.4.0-v1}
    container_name: redis-cluster
    environment:
      - REDIS_CLUSTER=yes
      - NODES=6
      - REPLICAS=1
      - TLS_ENABLED=yes
      - PORT=16379
      - TLS_PORT=27379
    command: ${RED<PERSON>_EXTRA_ARGS:---enable-debug-command yes --enable-module-command yes --tls-cluster yes --tls-auth-clients optional --save "" --cluster-announce-ip 127.0.0.1}
    ports:
      - "16379-16384:16379-16384"
      - "27379-27384:27379-27384"
    volumes:
      - "./cluster:/redis/work"
    profiles:
      - cluster
      - all