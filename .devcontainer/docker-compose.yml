---
version: "3.8"
services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
    networks:
      - redis
    command: sleep infinity
    environment:
      REDIS: "redis-stack-edge:6379" # default targeted Redis version
      REDIS__7_2_0: "redis-stack-7.2.0:6379"
      REDIS__6_2_6: "redis-stack-6.2.6:6379"
      REDIS__edge: "redis-stack-edge:6379"

  redis-stack-7.2.0:
    image: redis/redis-stack-server:7.2.0-RC3
    restart: unless-stopped
    networks:
      - redis

  redis-stack-6.2.6:
    image: redis/redis-stack-server:6.2.6-v9
    restart: unless-stopped
    networks:
      - redis

  redis-stack-edge:
    image: redis/redis-stack-server:edge
    restart: unless-stopped
    networks:
      - redis

networks:
  # defines shared network for communicating with Redis
  redis:
