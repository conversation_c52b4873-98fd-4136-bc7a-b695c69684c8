{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj"], "problemMatcher": "$msCompile"}, {"label": "test 6.2.6", "dependsOn": [], "options": {"env": {"REDIS": "${env:REDIS__6_2_6}"}}, "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj", "/p:CollectCoverage=true", "/p:CoverletOutputFormat=lcov", "/p:CoverletOutput=./lcov.info"], "problemMatcher": "$msCompile"}, {"label": "test 7.2.0", "dependsOn": [], "options": {"env": {"REDIS": "${env:REDIS__7_2_0}"}}, "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj", "/p:CollectCoverage=true", "/p:CoverletOutputFormat=lcov", "/p:CoverletOutput=./lcov.info"], "problemMatcher": "$msCompile"}, {"label": "test edge", "dependsOn": [], "options": {"env": {"REDIS": "${env:REDIS__edge}"}}, "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/tests/NRedisStack.Tests/NRedisStack.Tests.csproj", "/p:CollectCoverage=true", "/p:CoverletOutputFormat=lcov", "/p:CoverletOutput=./lcov.info"], "problemMatcher": "$msCompile"}, {"label": "test all", "dependsOrder": "sequence", "dependsOn": ["test 6.2.6", "test 7.2.0", "test edge"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}}]}