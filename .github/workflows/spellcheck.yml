name: spellcheck

on:
  pull_request:

concurrency:
  group: ${{ github.event.pull_request.number || github.ref }}-spellcheck
  cancel-in-progress: true

jobs:
  check-spelling:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Check Spelling
        uses: rojopolis/spellcheck-github-actions@0.35.0
        with:
          config_path: .github/spellcheck-settings.yml
          task_name: Markdown
