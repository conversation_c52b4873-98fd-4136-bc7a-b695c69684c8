name: Build and Test
on:
    workflow_call:
      inputs:

        redis_stack_type:
            required: true
            type: string

        # Target .NET framework, i.e. "net6.0".
        clr_version:
            required: true
            type: string

        # SDK version, as obtained when running `dotnet --list-sdks`,
        # i.e. "6.0.418", although it is safe to use '6.0.0' because
        # in global.json we set the rollForward strategy to "latestMinor".
        dotnet_sdk_version:
          required: true
          type: string

        mode:
            required: true
            type: string
jobs:
    build_and_test:
        name: Test
        runs-on: ubuntu-latest

        env:
          USER_NAME: ${{ secrets.USER_NAME }}
          PASSWORD: ${{ secrets.PASSWORD }}
          ENDPOINT: ${{ secrets.ENDPOINT }}
        steps:
          - uses: actions/checkout@v3

          - name: .NET Core 8
            uses: actions/setup-dotnet@v3
            with:
              dotnet-version: |
                6
                7
                8

          - name: Run redis-stack-server docker
            working-directory: .github
            run: docker compose up -d redis-stack-${{inputs.redis_stack_type}}

          - name: Set .env variables
            uses: xom9ikk/dotenv@v2
            with:
              path: .github/workflows/modes
              mode: ${{ inputs.mode }}

          # Make sure only the desired dotnet version is set both as target and as active SDK.
          - name: Tweak target frameworks
            run: |
              find . -name '*.csproj' | xargs -I {} sed -E -i 's|<TargetFrameworks(.*)>.*</TargetFrameworks>|<TargetFramework\1>${{inputs.clr_version}}</TargetFramework>|' {}
              find . -name '*.csproj' | xargs cat
              jq -n --arg version ${{inputs.dotnet_sdk_version}} '{"sdk":{"version":$version,"rollForward":"latestMinor"}}' > global.json
          - name: Check .NET version
            run: dotnet --version
          - name: Check .NET SDKs
            run: dotnet --list-sdks
          - name: Check .NET runtimes
            run: dotnet --list-runtimes
          - name: Restore dependencies
            run: dotnet restore
          - name: Build
            run: dotnet build --no-restore /p:ContinuousIntegrationBuild=true
          - name: Test
            run: |
              echo "${{secrets.REDIS_CA_PEM}}" > tests/NRedisStack.Tests/bin/Debug/${{inputs.clr_version}}/redis_ca.pem
              echo "${{secrets.REDIS_USER_CRT}}" > tests/NRedisStack.Tests/bin/Debug/${{inputs.clr_version}}/redis_user.crt
              echo "${{secrets.REDIS_USER_PRIVATE_KEY}}" > tests/NRedisStack.Tests/bin/Debug/${{inputs.clr_version}}/redis_user_private.key
              dotnet test -f ${{inputs.clr_version}} --no-build --verbosity normal /p:CollectCoverage=true /p:CoverletOutputFormat=opencover -p:BuildInParallel=false tests/Test.proj --logger GitHubActions
          - name: Codecov
            uses: codecov/codecov-action@v3
            with:
              token: ${{secrets.CODECOV_TOKEN}}
              verbose: true
          - name: Build
            run: dotnet pack -c Release

          - name: Test against Nuget package from local source
            if: inputs.redis_stack_type == 'edge'
            working-directory: PackageVerification
            run: |
              mkdir -p test-source
              dotnet nuget add source $(readlink -f test-source) -n test-source
              find .. -name '*.nupkg' | xargs -I {} dotnet nuget push {} -s test-source
              ls -R
              dotnet nuget remove source nuget.org
              dotnet nuget list source
              find . -name '*.csproj' | xargs -I {} sed -E -i 's|<TargetFrameworks(.*)>.*</TargetFrameworks>|<TargetFramework\1>${{inputs.clr_version}}</TargetFramework>|' {}
              dotnet restore -s test-source -v detailed
              dotnet run
