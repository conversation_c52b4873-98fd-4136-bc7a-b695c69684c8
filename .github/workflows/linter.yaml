name: Format check on pull request

on: pull_request

concurrency:
  group: ${{ github.event.pull_request.number || github.ref }}-linter
  cancel-in-progress: true

jobs:
  dotnet-format:
    runs-on: windows-latest
    steps:
      - name: Install dotnet-format
        run: dotnet tool install -g dotnet-format

      - name: Checkout repo
        uses: actions/checkout@v3

      - name: lint
        run: |
          dotnet format

      - name: Check for modified files
        run: |
          if (git status | Select-String -Pattern 'modified') {
            Write-Host "Modified files found. Please run dotnet format and commit the changes."
            exit 1
          } else {
            Write-Host "No modified files found."
          }
