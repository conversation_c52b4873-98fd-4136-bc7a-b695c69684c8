name: Integration Tests

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '**/*.md'
    branches:
      - master
  pull_request:
  schedule:
    - cron: "0 1 * * *"

concurrency:
  group: ${{ github.event.pull_request.number || github.ref }}-integration
  cancel-in-progress: true

env:
  CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
  CURRENT_REDIS_VERSION: '7.4.1'

jobs:
    redis_version:
      runs-on: ubuntu-latest
      outputs:
        CURRENT: ${{ env.CURRENT_REDIS_VERSION }}
      steps:
        - name: Compute outputs
          run: |
            echo "CURRENT=${{ env.CURRENT_REDIS_VERSION }}" >> $GITHUB_OUTPUT

    tests:
      runs-on: ubuntu-latest
      timeout-minutes: 60
      needs: redis_version
      strategy:
        max-parallel: 15
        fail-fast: false
        matrix:
          redis-version: [ '8.0-RC2-pre', '${{ needs.redis_version.outputs.CURRENT }}', '7.2.6', '6.2.16']
          dotnet-version: ['6.0', '7.0', '8.0']
      env:
        ACTIONS_ALLOW_UNSECURE_COMMANDS: true
      name: Redis ${{ matrix.redis-version }}; .NET ${{ matrix.dotnet-version }};
      steps:
        - uses: actions/checkout@v4
        - name: Run tests
          uses: ./.github/actions/run-tests
          with:
              dotnet-version: ${{ matrix.dotnet-version }}              
              redis-version: ${{ matrix.redis-version }}
              REDIS_CA_PEM: ${{ secrets.REDIS_CA_PEM }}
              REDIS_USER_CRT: ${{ secrets.REDIS_USER_CRT }}
              REDIS_USER_PRIVATE_KEY: ${{ secrets.REDIS_USER_PRIVATE_KEY }}

    build_and_test_windows:
      name: Windows Test ${{matrix.redis-stack-version}}
      runs-on: windows-latest
      strategy:
        fail-fast: false
        matrix:
          redis-stack-version: ['6.2.6-v18', '7.2.0-v14', '7.4.0-v2']
      env:
        redis_stack_version: ${{matrix.redis-stack-version}}
        USER_NAME: ${{ secrets.USER_NAME }}
        PASSWORD: ${{ secrets.PASSWORD }}
        ENDPOINT: ${{ secrets.ENDPOINT }}
      steps:
        - uses: actions/checkout@v3
        - uses: Vampire/setup-wsl@v2
          with:
            distribution: Ubuntu-22.04
        - name: Install Redis
          shell: wsl-bash {0}
          run: |
            sudo apt-get update
            sudo apt-get install curl gpg lsb-release libgomp1 jq -y
            curl https://packages.redis.io/redis-stack/redis-stack-server-${{env.redis_stack_version}}.jammy.x86_64.tar.gz -o redis-stack.tar.gz
            tar xf redis-stack.tar.gz
        - name: Restore dependencies
          run: dotnet restore
        - name: Build
          run: dotnet build --no-restore /p:ContinuousIntegrationBuild=true
        - name: Save test certificates
          shell: wsl-bash {0}
          run: |
            export TARGET_DIR=tests/NRedisStack.Tests/bin/Debug/net481
            echo "${{secrets.REDIS_CA_PEM}}" > ${TARGET_DIR}/redis_ca.pem
            echo "${{secrets.REDIS_USER_CRT}}" > ${TARGET_DIR}/redis_user.crt
            echo "${{secrets.REDIS_USER_PRIVATE_KEY}}" > ${TARGET_DIR}redis_user_private.key
            
            # We need to remove the cluster endpoint from the endpoints.json file 
            # because we run only standalone redis-stack-server.
            jq 'del(.cluster)' tests/dockers/endpoints.json > standalone_only.json
            rm tests/dockers/endpoints.json && cp standalone_only.json tests/dockers/endpoints.json
            cp -f standalone_only.json ${TARGET_DIR}/endpoints.json
            cat tests/dockers/endpoints.json
            cat ${TARGET_DIR}/endpoints.json            
        - name: Run redis-server
          shell: wsl-bash {0}
          run: |
            ./redis-stack-server-${{env.redis_stack_version}}/bin/redis-stack-server &
            sleep 3
            ./redis-stack-server-${{env.redis_stack_version}}/bin/redis-cli INFO SERVER | grep redis_version
        - name: Test
          shell: cmd
          env:
            REDIS_VERSION: ${{env.redis_stack_version}}
          run: |
            rem Extract part of the version before the '-' character
            for /f "tokens=1 delims=-" %%a in ("%REDIS_VERSION%") do set REDIS_VERSION=%%a
            echo %REDIS_VERSION%
            dotnet test -f net481 --no-build --verbosity detailed -p:BuildInParallel=false tests/Test.proj --logger GitHubActions
