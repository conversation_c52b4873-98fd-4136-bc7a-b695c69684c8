name-template: '$NEXT_MINOR_VERSION'
tag-template: 'v$NEXT_MINOR_VERSION'
filter-by-commitish: true
commitish: master
autolabeler:
  - label: 'maintenance'
    files:
      - '*.md'
      - '.github/*'
  - label: 'bug'
    branch:
      - '/bug-.+'
  - label: 'maintenance'
    branch:
      - '/maintenance-.+'
  - label: 'feature'
    branch:
      - '/feature-.+'
categories:
  - title: '🔥 Breaking Changes'
    labels:
      - 'breakingchange'
  - title: '🧪 Experimental Features'
    labels:
      - 'experimental'
  - title: '🚀 New Features'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
      - 'BUG'
  - title: '🧰 Maintenance'
    labels:
      - 'maintenance'
      - 'dependencies'
      - 'documentation'
      - 'docs'
      - 'testing'
change-template: '- $TITLE (#$NUMBER)'
exclude-labels:
  - 'skip-changelog'
template: |
  # Changes

  $CHANGES

  ## Contributors
  We'd like to thank all the contributors who worked on this release!

  $CONTRIBUTORS

